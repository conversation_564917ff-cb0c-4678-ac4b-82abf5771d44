
import { useState, useEffect } from "react";
import { useSupabase } from "./useSupabase";
import { Tutorial } from "./useTutorials";

export type ChapterFile = {
  url: string;
  filename: string;
  content?: string;
};

export type TutorialDetails = Tutorial & {
  indexContent: string;
  chapters: ChapterFile[];
};

export const useTutorialDetails = (id: string) => {
  const [tutorial, setTutorial] = useState<TutorialDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();

  useEffect(() => {
    const fetchTutorial = async () => {
      try {
        setLoading(true);
        
        // Fetch tutorial metadata - RLS will handle access control
        const { data: tutorialData, error: tutorialError } = await supabase
          .from("tutorial_metadata")
          .select("*")
          .eq("id", id)
          .eq("is_public", true)
          .single();

        if (tutorialError) throw new Error(tutorialError.message);
        if (!tutorialData) throw new Error("Tutorial not found");
        
        // Fetch the index content
        const indexResponse = await fetch(tutorialData.index_url);
        if (!indexResponse.ok) throw new Error("Failed to fetch index file");
        const indexContent = await indexResponse.text();
        
        // Parse chapter URLs from the chapter_urls field
        let chapters: ChapterFile[] = [];
        if (tutorialData.chapter_urls && Array.isArray(tutorialData.chapter_urls)) {
          chapters = tutorialData.chapter_urls as ChapterFile[];
          
          // Fetch content for each chapter
          const chapterPromises = chapters.map(async (chapter) => {
            try {
              const response = await fetch(chapter.url);
              if (!response.ok) throw new Error(`Failed to fetch chapter: ${chapter.filename}`);
              const content = await response.text();
              return { ...chapter, content };
            } catch (err) {
              console.error(`Error loading chapter ${chapter.filename}:`, err);
              return { ...chapter, content: "# Error loading chapter content" };
            }
          });
          
          chapters = await Promise.all(chapterPromises);
        }
        
        // Format the tutorial data
        const formattedTutorial: TutorialDetails = {
          id: tutorialData.id,
          title: tutorialData.project_name,
          description: tutorialData.description || "",
          repoUrl: tutorialData.repo_url || "",
          createdAt: tutorialData.created_at,
          tags: tutorialData.tags || [],
          chaptersCount: chapters.length,
          difficulty: tutorialData.difficulty,
          language: tutorialData.language,
          views: tutorialData.views || 0,
          featured: tutorialData.featured || false,
          imageSrc: tutorialData.cover_url,
          backgroundColor: tutorialData.background_color,
          isPublic: tutorialData.is_public || false,
          indexContent,
          chapters
        };

        setTutorial(formattedTutorial);
        
        // Increment view count in a separate call to ensure it happens
        const { error: viewError } = await supabase
        .from("tutorial_metadata")
        .update({ views: (tutorialData.views || 0) + 1 })
        .eq("id", id);
      
        if (viewError) {
          console.error("Error updating view count:", viewError);
        }

      } catch (err) {
        console.error("Error fetching tutorial details:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch tutorial details");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchTutorial();
    }
  }, [id, supabase]);

  return { tutorial, loading, error };
};
