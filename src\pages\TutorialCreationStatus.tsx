import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import NavBar from "@/components/NavBar";
import { Progress } from "@/components/ui/progress";
import { create_tutorial_flow } from "@/Agents/Code2Documentation/flow";
import { SharedStore } from "@/Agents/Code2Documentation/types";
import { agentEvents, AgentEventType, ProgressEvent } from "@/Agents/Code2Documentation/utils/events";
import { CheckCircle, Clock, AlertCircle, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useUser } from "@clerk/clerk-react";

// Types for generation stages
type StageStatus = 'pending' | 'in-progress' | 'completed' | 'error';

type ChapterInfo = {
  number: number;
  title: string;
  status: StageStatus;
};

type GenerationStage = {
  id: string;
  name: string;
  status: StageStatus;
  description: string;
  completedIn: string;
  details: string[];
  chapters?: ChapterInfo[];
};

const TutorialCreationStatus = () => {
  // Get location state with parameters from the previous page
  const location = useLocation();
  const navigate = useNavigate();

  const { user } = useUser();
  // Use useMemo to prevent re-renders
  const params = React.useMemo(() => location.state || {}, [location.state]);
  console.log("Page Input Params:", params);

  // Overall progress
  const [progress, setProgress] = useState(68);

  // Graph status tracking
  const [graphStatus, setGraphStatus] = useState({
    currentNode: "",
    nodeProgress: 0,
    statusMessage: ""
  });

  // Process log entries
  const [logEntries, setLogEntries] = useState<{timestamp: string, message: string, type: 'info' | 'status' | 'error'}[]>([]);

  // Reference to the log container for autoscroll
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Generation stages tracking
  const [generationStages, setGenerationStages] = useState<GenerationStage[]>([
    {
      id: "repository-fetching",
      name: "Repository Fetching",
      status: "pending", // pending, in-progress, completed, error
      description: "",
      completedIn: "",
      details: []
    },
    {
      id: "code-analysis",
      name: "Code Analysis",
      status: "pending",
      description: "",
      completedIn: "",
      details: []
    },
    {
      id: "abstraction-identification",
      name: "Core Abstractions Identification",
      status: "pending",
      description: "",
      completedIn: "",
      details: []
    },
    {
      id: "chapter-planning",
      name: "Chapter Planning",
      status: "pending",
      description: "",
      completedIn: "",
      details: []
    },
    {
      id: "content-generation",
      name: "Content Generation",
      status: "pending",
      description: "",
      completedIn: "",
      details: [],
      chapters: []
    }
  ]);

  useEffect(() => {
    // Get repository URL from params or use default
    const repoUrl = params?.repoUrl || "https://github.com/EarthShaping/testautocode";
    handleGenerateTutorial(repoUrl, params);
  }, [params]);

  // Autoscroll log container to bottom when new entries are added
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logEntries]);

  useEffect(() => {
    // Helper function to get current timestamp
    const getTimestamp = () => {
      const now = new Date();
      return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    };

    // Helper function to format time duration
    const formatDuration = (startTime: number) => {
      const duration = Date.now() - startTime;
      if (duration < 60000) {
        return `${Math.floor(duration / 1000)} seconds`;
      } else {
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
      }
    };

    // Map node names to generation stages
    const nodeToStageMap: Record<string, string> = {
      'FetchRepo': 'repository-fetching',
      'IdentifyAbstractions': 'abstraction-identification',
      'AnalyzeRelationships': 'abstraction-identification',
      'OrderChapters': 'chapter-planning',
      'WriteChapters': 'content-generation',
      'CombineTutorial': 'content-generation'
    };

    // Track stage start times
    const stageStartTimes: Record<string, number> = {};

    // Subscribe to progress events
    const handleProgress = (data: ProgressEvent) => {
      console.log(`Progress update: ${data.stage} - ${data.progress}%`);
      setProgress(data.progress);

      // Add to log
      setLogEntries(prev => [
        ...prev,
        {
          timestamp: getTimestamp(),
          message: `Progress update: ${data.stage} - ${data.progress}%${data.message ? ` - ${data.message}` : ''}`,
          type: 'info'
        }
      ]);

      // Update generation stages based on progress
      if (data.stage === "Repository Fetching" && data.progress > 0) {
        setGenerationStages(prev => {
          const newStages = [...prev];
          const stageIndex = newStages.findIndex(s => s.id === "repository-fetching");
          if (stageIndex !== -1) {
            if (newStages[stageIndex].status !== "completed") {
              if (data.progress >= 100) {
                newStages[stageIndex].status = "completed";
                newStages[stageIndex].description = data.message || "Successfully fetched repository files";
                if (!stageStartTimes["repository-fetching"]) {
                  stageStartTimes["repository-fetching"] = Date.now() - 10000; // Approximate if we missed the start
                }
                newStages[stageIndex].completedIn = formatDuration(stageStartTimes["repository-fetching"]);
              } else if (newStages[stageIndex].status !== "in-progress") {
                newStages[stageIndex].status = "in-progress";
                stageStartTimes["repository-fetching"] = Date.now();
              }
            }
          }
          return newStages;
        });
      } else if (data.stage.includes("Chapter") && data.progress > 0) {
        // Handle chapter writing progress
        const chapterMatch = data.message?.match(/Writing chapter (\d+) of (\d+)/);
        if (chapterMatch) {
          const currentChapter = parseInt(chapterMatch[1]);
          const totalChapters = parseInt(chapterMatch[2]);

          setGenerationStages(prev => {
            const newStages = [...prev];
            const contentStageIndex = newStages.findIndex(s => s.id === "content-generation");

            if (contentStageIndex !== -1) {
              // Ensure chapters array is initialized
              if (!newStages[contentStageIndex].chapters) {
                newStages[contentStageIndex].chapters = [];
              }

              // Create or update chapters
              for (let i = 1; i <= totalChapters; i++) {
                const existingChapterIndex = newStages[contentStageIndex].chapters.findIndex(c => c.number === i);

                if (existingChapterIndex === -1) {
                  // Create new chapter entry
                  newStages[contentStageIndex].chapters.push({
                    number: i,
                    status: i < currentChapter ? "completed" : i === currentChapter ? "in-progress" : "pending",
                    title: `Chapter ${i}`
                  });
                } else {
                  // Update existing chapter entry
                  newStages[contentStageIndex].chapters[existingChapterIndex].status =
                    i < currentChapter ? "completed" : i === currentChapter ? "in-progress" : "pending";
                }
              }

              // Update content generation stage status
              if (newStages[contentStageIndex].status !== "completed") {
                newStages[contentStageIndex].status = "in-progress";
                newStages[contentStageIndex].description = `Generating chapter ${currentChapter} of ${totalChapters}`;

                if (!stageStartTimes["content-generation"]) {
                  stageStartTimes["content-generation"] = Date.now();
                }
              }
            }

            return newStages;
          });
        }
      }
    };

    // Subscribe to graph status events
    const handleGraphStatus = (data: ProgressEvent) => {
      console.log(`Graph status update: ${data.stage} - ${data.progress}% - ${data.message || ''}`);
      setGraphStatus({
        currentNode: data.stage,
        nodeProgress: data.progress,
        statusMessage: data.message || ''
      });

      // Add to log
      setLogEntries(prev => [
        ...prev,
        {
          timestamp: getTimestamp(),
          message: `${data.stage}: ${data.message || `Progress ${data.progress}%`}`,
          type: 'status'
        }
      ]);

      // Update generation stages based on node activity
      const stageId = nodeToStageMap[data.stage];
      if (stageId) {
        setGenerationStages(prev => {
          const newStages = [...prev];
          const stageIndex = newStages.findIndex(s => s.id === stageId);

          if (stageIndex !== -1) {
            // If this is the first time we're seeing this stage active
            if (newStages[stageIndex].status !== "in-progress" && newStages[stageIndex].status !== "completed") {
              newStages[stageIndex].status = "in-progress";
              stageStartTimes[stageId] = Date.now();

              // Mark previous stages as completed if they're still pending
              for (let i = 0; i < stageIndex; i++) {
                if (newStages[i].status === "pending") {
                  newStages[i].status = "completed";
                  newStages[i].description = "Completed";
                  newStages[i].completedIn = "< 1 second"; // We missed tracking this stage
                }
              }
            }

            // Update description based on the message
            if (data.message) {
              newStages[stageIndex].description = data.message;
            }

            // If the node is complete, mark the stage as complete
            if (data.progress >= 100) {
              newStages[stageIndex].status = "completed";
              if (stageStartTimes[stageId]) {
                newStages[stageIndex].completedIn = formatDuration(stageStartTimes[stageId]);
              }

              // Special handling for specific nodes
              if (data.stage === "IdentifyAbstractions") {
                // Extract abstraction details from the message
                const abstractionMatch = data.message.match(/(\d+) core abstractions/);
                if (abstractionMatch) {
                  const abstractionCount = abstractionMatch[1];
                  newStages[stageIndex].description = `Identified ${abstractionCount} core abstractions`;
                }
              } else if (data.stage === "OrderChapters") {
                const chapterMatch = data.message.match(/(\d+) chapters/);
                if (chapterMatch) {
                  const chapterCount = chapterMatch[1];
                  newStages[stageIndex].description = `Determined optimal order for ${chapterCount} chapters`;
                }
              }
            }
          }

          return newStages;
        });
      }

      // Special handling for WriteChapters node to track individual chapters
      if (data.stage === "WriteChapters") {
        const chapterMatch = data.message?.match(/chapter (\d+)/i);
        if (chapterMatch) {
          const chapterNumber = parseInt(chapterMatch[1]);
          const chapterName = data.message?.replace(/^.*?chapter \d+:\s*/i, "").trim();

          setGenerationStages(prev => {
            const newStages = [...prev];
            const contentStageIndex = newStages.findIndex(s => s.id === "content-generation");

            if (contentStageIndex !== -1) {
              // Ensure chapters array is initialized
              if (!newStages[contentStageIndex].chapters) {
                newStages[contentStageIndex].chapters = [];
              }

              // Find or create the chapter entry
              const existingChapterIndex = newStages[contentStageIndex].chapters.findIndex(c => c.number === chapterNumber);

              if (existingChapterIndex === -1) {
                // Create new chapter entry
                newStages[contentStageIndex].chapters.push({
                  number: chapterNumber,
                  status: data.progress >= 100 ? "completed" : "in-progress",
                  title: chapterName || `Chapter ${chapterNumber}`
                });
              } else {
                // Update existing chapter entry
                newStages[contentStageIndex].chapters[existingChapterIndex].status =
                  data.progress >= 100 ? "completed" : "in-progress";
                if (chapterName) {
                  newStages[contentStageIndex].chapters[existingChapterIndex].title = chapterName;
                }
              }

              // Sort chapters by number
              newStages[contentStageIndex].chapters.sort((a, b) => a.number - b.number);
            }

            return newStages;
          });
        }
      }
    };

    // Subscribe to error events
    const handleError = (error: Error) => {
      console.error('Error in flow:', error);

      // Add to log
      setLogEntries(prev => [
        ...prev,
        {
          timestamp: getTimestamp(),
          message: `Error: ${error.message}`,
          type: 'error'
        }
      ]);

      // Update the current stage to error state
      if (graphStatus.currentNode) {
        const stageId = nodeToStageMap[graphStatus.currentNode];
        if (stageId) {
          setGenerationStages(prev => {
            const newStages = [...prev];
            const stageIndex = newStages.findIndex(s => s.id === stageId);

            if (stageIndex !== -1) {
              newStages[stageIndex].status = "error";
              newStages[stageIndex].description = error.message;
            }

            return newStages;
          });
        }
      }
    };

    // Subscribe to complete events
    const handleComplete = (result: any) => {
      console.log('Flow completed:', result);

      // Add to log
      setLogEntries(prev => [
        ...prev,
        {
          timestamp: getTimestamp(),
          message: `Tutorial generation completed successfully`,
          type: 'info'
        }
      ]);

      // Mark all in-progress stages as completed
      setGenerationStages(prev => {
        const newStages = [...prev];

        // Update all in-progress stages to completed
        newStages.forEach(stage => {
          if (stage.status === 'in-progress') {
            stage.status = 'completed';
            if (!stage.completedIn) {
              stage.completedIn = 'just now';
            }
          }

          // Also update any in-progress chapters
          if (stage.chapters) {
            stage.chapters.forEach(chapter => {
              if (chapter.status === 'in-progress') {
                chapter.status = 'completed';
              }
            });
          }
        });

        return newStages;
      });

      // Show success toast
      toast({
        title: "Tutorial Generated",
        description: "Your tutorial has been successfully generated!",
        variant: "default"
      });

      // Redirect to the tutorial if we have a tutorial ID
      if (result.tutorialId) {
        console.log('Redirecting to tutorial:', result.tutorialId);
        // Add a small delay to let the user see the completion message
        setTimeout(() => {
          navigate(`/tutorial/${result.tutorialId}`);
        }, 2000);
      }
    };

    agentEvents.on(AgentEventType.PROGRESS, handleProgress);
    agentEvents.on(AgentEventType.GRAPH_STATUS, handleGraphStatus);
    agentEvents.on(AgentEventType.ERROR, handleError);
    agentEvents.on(AgentEventType.COMPLETE, handleComplete);

    // Cleanup listeners on unmount
    return () => {
      agentEvents.off(AgentEventType.PROGRESS, handleProgress);
      agentEvents.off(AgentEventType.GRAPH_STATUS, handleGraphStatus);
      agentEvents.off(AgentEventType.ERROR, handleError);
      agentEvents.off(AgentEventType.COMPLETE, handleComplete);
    };
  }, [graphStatus.currentNode, navigate]);

  const handleGenerateTutorial = async (repoUrl: string, options: any = {}) => {
    try {
      
      // Use selected files from the UI instead of patterns
      const selectedFiles: string[] = options.selectedFiles || [];

      // Create shared data
      const shared: SharedStore = {
        user_id: user?.id || "",
      //  session_id: "tutorial-session", // TODO: Generate a unique session ID

        repo_url: repoUrl, // Use the provided repo URL
        //local_dir:"C:/DEV_AI/GenerativePangea/repo-tutorial-forge/src/pocketflow",
        //project_name: options.projectName, // Can be None, FetchRepo will derive it
        github_token: options.githubToken || "",
        output_dir: "./output", //Base directory for CombineTutorial output

        // Selected files to process (replaces pattern-based filtering)
        selected_files: selectedFiles,

        // Language for multi-language support
        language: options.contentLanguage || "english",

        // Use cache flag (inverse of no-cache flag)
        use_cache: options.advancedOptions?.cacheDuration !== 0, // If cache duration is 0, don't use cache

        // Max abstraction number parameter
        max_abstraction_num: options.maxAbstractions || 10, // default is 10

        final_output_dir: "./testweb",

        // Additional parameters can be added here as needed
      };
      //console.log("Shared",shared);
      const flow = create_tutorial_flow();
      await flow.run(shared);
    } catch (error) {
      console.error("Error running tutorial flow:", error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NavBar />
      <main className="container mx-auto px-4 py-8 md:py-12">
        {/* Page Title */}
        <div className="mb-8">
          <div className="flex items-center mb-2">
            <Link
              to="/"
              className="text-primary-600 hover:text-primary-700 cursor-pointer"
            >
              <i className="fa-solid fa-arrow-left mr-2"></i>
              Back to configuration
            </Link>
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
            Tutorial Generation Progress
          </h1>
          <p className="text-gray-600 max-w-3xl">
            We're analyzing the repository and generating your tutorial. This
            process may take several minutes depending on the size and
            complexity of the codebase.
          </p>
        </div>

        {/* Main Progress Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start">
              <div className="bg-gray-100 rounded-lg p-3 mr-4">
                <i className="fa-brands fa-github text-gray-700 text-3xl"></i>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-800">
                  {params.repoUrl ? params.repoUrl.split('/').pop() : 'react-query'}
                </h2>
                <p className="text-gray-600">
                  {params.repoUrl || 'https://github.com/tannerlinsley/react-query'}
                </p>
                <div className="flex items-center mt-1">
                  <span className="flex items-center text-sm text-gray-500 mr-4">
                    <i className="fa-regular fa-file-code mr-1"></i>
                    247 files
                  </span>
                  <span className="flex items-center text-sm text-gray-500">
                    <i className="fa-solid fa-code mr-1"></i>
                    {params.programmingLanguage === 'auto' ? 'Auto-detected' :
                     params.programmingLanguage ? params.programmingLanguage.charAt(0).toUpperCase() + params.programmingLanguage.slice(1) :
                     'JavaScript, TypeScript'}
                  </span>
                </div>
              </div>
            </div>
            <div className="bg-primary-50 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-primary-700 mb-1">
                {progress}%
              </div>
              <div className="text-sm text-primary-600">Overall Progress</div>
            </div>
          </div>

          {/* Overall Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-gray-700">
                Overall Progress
              </h3>
              <span className="text-sm text-gray-600">
                {progress}% Complete
              </span>
            </div>
            <Progress value={progress} className="h-4" />
          </div>

          {/* Current Node Activity */}
          {graphStatus.currentNode && (
            <div className={`mb-8 p-4 rounded-lg border ${
              progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                ? 'bg-green-50 border-green-100'
                : 'bg-primary-50 border-primary-100'
            }`}>
              <div className="flex justify-between items-center mb-2">
                <h3 className={`text-sm font-medium ${
                  progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                    ? 'text-green-700'
                    : 'text-primary-700'
                }`}>
                  Current Activity
                </h3>
                <span className={`text-sm ${
                  progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                    ? 'text-green-600'
                    : 'text-primary-600'
                }`}>
                  {progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                    ? '100% Complete'
                    : `${graphStatus.nodeProgress}% Complete`}
                </span>
              </div>
              <div className="flex items-center mb-3">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                  progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                    ? 'bg-green-100'
                    : 'bg-primary-100'
                }`}>
                  {progress === 100 && !generationStages.some(s => s.status === 'in-progress') ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Loader2 className="h-5 w-5 text-primary-600 animate-spin" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">{graphStatus.currentNode}</h4>
                  <p className="text-sm text-gray-600">
                    {progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                      ? "Completed successfully"
                      : graphStatus.statusMessage}
                  </p>
                </div>
              </div>
              <Progress
                value={progress === 100 && !generationStages.some(s => s.status === 'in-progress') ? 100 : graphStatus.nodeProgress}
                className={`h-2 ${progress === 100 && !generationStages.some(s => s.status === 'in-progress') ? 'bg-green-100' : 'bg-primary-100'}`}
              />
            </div>
          )}

          {/* Tutorial Generation Stages */}
          <div className="mb-8">
            <h3 className="text-sm font-medium text-gray-700 mb-4">
              Tutorial Generation Stages
            </h3>

            <div className="space-y-6">
              {generationStages.map((stage) => (
                <div key={stage.id} className="border rounded-lg overflow-hidden">
                  <div className={`p-4 flex items-center justify-between ${
                    stage.status === 'completed' ? 'bg-green-50' :
                    stage.status === 'in-progress' ? 'bg-blue-50' :
                    stage.status === 'error' ? 'bg-red-50' : 'bg-gray-50'
                  }`}>
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                        stage.status === 'completed' ? 'bg-green-100' :
                        stage.status === 'in-progress' ? 'bg-blue-100' :
                        stage.status === 'error' ? 'bg-red-100' : 'bg-gray-100'
                      }`}>
                        {stage.status === 'completed' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : stage.status === 'in-progress' ? (
                          <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
                        ) : stage.status === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <Clock className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <h4 className={`font-medium ${
                          stage.status === 'completed' ? 'text-green-800' :
                          stage.status === 'in-progress' ? 'text-blue-800' :
                          stage.status === 'error' ? 'text-red-800' : 'text-gray-800'
                        }`}>
                          {stage.name}
                        </h4>
                        {stage.description && (
                          <p className={`text-sm ${
                            stage.status === 'completed' ? 'text-green-600' :
                            stage.status === 'in-progress' ? 'text-blue-600' :
                            stage.status === 'error' ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {stage.description}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="text-right">
                      <span className={`text-xs font-medium ${
                        stage.status === 'completed' ? 'text-green-600' :
                        stage.status === 'in-progress' ? 'text-blue-600' :
                        stage.status === 'error' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {stage.status === 'completed' ? 'Completed' :
                         stage.status === 'in-progress' ? 'In Progress' :
                         stage.status === 'error' ? 'Error' : 'Pending'}
                      </span>
                      {stage.completedIn && (
                        <p className="text-xs text-gray-500">
                          Completed in {stage.completedIn}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Chapter list for Content Generation stage */}
                  {stage.id === 'content-generation' && stage.chapters && stage.chapters.length > 0 && (
                    <div className="p-4 bg-white">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Chapters</h5>
                      <div className="space-y-2">
                        {stage.chapters.map((chapter) => (
                          <div key={chapter.number} className="flex items-center">
                            <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                              chapter.status === 'completed' ? 'bg-green-100' :
                              chapter.status === 'in-progress' ? 'bg-blue-100' : 'bg-gray-100'
                            }`}>
                              {chapter.status === 'completed' ? (
                                <CheckCircle className="h-3 w-3 text-green-600" />
                              ) : chapter.status === 'in-progress' ? (
                                <Loader2 className="h-3 w-3 text-blue-600 animate-spin" />
                              ) : (
                                <Clock className="h-3 w-3 text-gray-400" />
                              )}
                            </div>
                            <span className={`text-sm ${
                              chapter.status === 'completed' ? 'text-green-700' :
                              chapter.status === 'in-progress' ? 'text-blue-700' : 'text-gray-700'
                            }`}>
                              {chapter.title}
                            </span>
                            {chapter.status === 'in-progress' && (
                              <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                In progress
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Details for other stages */}
                  {stage.id !== 'content-generation' && stage.details && stage.details.length > 0 && (
                    <div className="p-4 bg-white">
                      <div className="space-y-1">
                        {stage.details.map((detail, index) => (
                          <div key={index} className="text-sm text-gray-700">
                            {detail}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Real-time Log */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700">Process Log</h3>
              <button className="text-xs text-primary-600 hover:text-primary-700 flex items-center">
                <i className="fa-solid fa-expand mr-1"></i>
                Expand Log
              </button>
            </div>
            <div
              ref={logContainerRef}
              className="bg-gray-900 text-gray-200 rounded-lg p-4 h-[200px] overflow-y-auto font-mono text-xs"
            >
              <div className="space-y-2">
                {logEntries.length > 0 ? (
                  logEntries.map((entry, index) => (
                    <div className="flex" key={index}>
                      <span
                        className={`mr-2 ${
                          entry.type === 'error'
                            ? 'text-red-400'
                            : entry.type === 'status'
                              ? 'text-blue-400'
                              : 'text-green-400'
                        }`}
                      >
                        [{entry.timestamp}]
                      </span>
                      <span>{entry.message}</span>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="flex">
                      <span className="text-green-400 mr-2">[12:34:18]</span>
                      <span>
                        Starting tutorial generation for repository: react-query
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-green-400 mr-2">[12:34:20]</span>
                      <span>
                        Cloning repository from
                        https://github.com/tannerlinsley/react-query
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-green-400 mr-2">[12:34:36]</span>
                      <span>Repository cloned successfully (247 files, 1.2MB)</span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:34:38]</span>
                      <span>
                        FetchRepo: Preparing to fetch repository files
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:34:40]</span>
                      <span>FetchRepo: Starting file retrieval</span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:34:42]</span>
                      <span>FetchRepo: Crawling GitHub repository: https://github.com/tannerlinsley/react-query</span>
                    </div>
                    <div className="flex">
                      <span className="text-green-400 mr-2">[12:35:42]</span>
                      <span>Excluding node_modules/, dist/, and .test. files</span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:36:10]</span>
                      <span>
                        FetchRepo: GitHub repository files retrieved
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:36:15]</span>
                      <span>
                        FetchRepo: Retrieved 247 files
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:36:18]</span>
                      <span>
                        FetchRepo: Completed fetching 247 files
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:36:20]</span>
                      <span>
                        IdentifyAbstractions: Starting abstraction identification
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-green-400 mr-2">[12:36:22]</span>
                      <span>
                        Starting core abstraction identification with UX Pilot 3.7
                        Sonnet
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:38:30]</span>
                      <span>
                        IdentifyAbstractions: Analyzing code patterns
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-blue-400 mr-2">[12:38:35]</span>
                      <span>
                        IdentifyAbstractions: Completed abstraction identification
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <button
            type="button"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <i className="fa-solid fa-pause mr-1"></i>
            Pause Generation
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              className={`px-6 py-2 bg-primary-600 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                progress === 100 && !generationStages.some(s => s.status === 'in-progress')
                  ? 'hover:bg-primary-700'
                  : 'opacity-50 cursor-not-allowed'
              }`}
              disabled={progress !== 100 || generationStages.some(s => s.status === 'in-progress')}
            >
              <i className="fa-solid fa-download mr-1"></i>
              Download Preview
            </button>
          </div>
        </div>
      </main>

      {/* Status Toast */}
      {progress < 100 || generationStages.some(s => s.status === 'in-progress') ? (
        <div className="fixed bottom-4 right-4 bg-primary-600 text-white px-6 py-3 rounded-md flex items-center shadow-lg">
          <Loader2 className="h-5 w-5 text-white mr-3 animate-spin" />
          <span>
            {graphStatus.currentNode ? (
              <div>
                <div className="font-semibold">{graphStatus.currentNode}</div>
                <div className="text-sm">{graphStatus.statusMessage}</div>
                <div className="w-full bg-white/20 h-1 mt-1 rounded-full overflow-hidden">
                  <div
                    className="bg-white h-full rounded-full"
                    style={{ width: `${graphStatus.nodeProgress}%` }}
                  ></div>
                </div>
              </div>
            ) : (
              <span>
                {generationStages.find(s => s.status === 'in-progress')?.name || 'Initializing...'}
              </span>
            )}
          </span>
        </div>
      ) : (
        <div className="fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-md flex items-center shadow-lg">
          <CheckCircle className="h-5 w-5 text-white mr-3" />
          <span>Tutorial generation completed successfully!</span>
        </div>
      )}

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center">
                <i className="fa-solid fa-book-open text-primary-600 text-xl mr-2"></i>
                <span className="text-lg font-bold text-gray-800">
                  CodeTutor
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Automated tutorials from GitHub repositories
              </p>
            </div>

            <div className="flex space-x-6">
              <span className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-brands fa-github text-lg"></i>
              </span>
              <span className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-brands fa-twitter text-lg"></i>
              </span>
              <span className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-solid fa-envelope text-lg"></i>
              </span>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-6 pt-6 flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-600">
              © 2023 CodeTutor. All rights reserved.
            </div>
            <div className="mt-4 md:mt-0 flex space-x-6">
              <span className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">
                Privacy Policy
              </span>
              <span className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">
                Terms of Service
              </span>
              <span className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">
                Contact
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TutorialCreationStatus;
