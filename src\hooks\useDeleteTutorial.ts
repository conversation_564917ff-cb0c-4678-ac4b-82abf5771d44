
import { useState } from "react";
import { useSupabase } from "./useSupabase";
import { toast } from "@/hooks/use-toast";

export const useDeleteTutorial = () => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();

  const deleteTutorial = async (tutorialId: string, onSuccess?: () => void) => {
    try {
      setIsDeleting(true);
      setError(null);

      // 1. First, get the tutorial metadata to access file URLs
      // RLS will handle access control - user can only delete their own tutorials
      const { data: tutorialData, error: fetchError } = await supabase
        .from("tutorial_metadata")
        .select("*")
        .eq("id", tutorialId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch tutorial data: ${fetchError.message}`);
      }

      if (!tutorialData) {
        throw new Error("Tutorial not found or you don't have permission to delete it");
      }

      // 2. Delete files from storage
      // Extract file paths from chapter_urls and index_url
      const filesToDelete: string[] = [];

      // Add index file
      if (tutorialData.index_url) {
        try {
          const indexUrl = new URL(tutorialData.index_url);
          const indexPath = indexUrl.pathname.split('/').pop();
          if (indexPath) {
            filesToDelete.push(indexPath);
          }
        } catch (e) {
          console.error("Error parsing index URL:", e);
        }
      }

      // Add chapter files
      if (tutorialData.chapter_urls && Array.isArray(tutorialData.chapter_urls)) {
        tutorialData.chapter_urls.forEach((chapter: any) => {
          if (chapter.url) {
            try {
              const chapterUrl = new URL(chapter.url);
              const chapterPath = chapterUrl.pathname.split('/').pop();
              if (chapterPath) {
                filesToDelete.push(chapterPath);
              }
            } catch (e) {
              console.error("Error parsing chapter URL:", e);
            }
          }
        });
      }

      // Delete files from storage if there are any to delete
      if (filesToDelete.length > 0) {
        // Note: This is a simplified approach. In a real app, you'd need to
        // determine the correct bucket and path structure
        const { error: storageError } = await supabase.storage
          .from('tutorials')
          .remove(filesToDelete);

        if (storageError) {
          console.error("Error deleting files from storage:", storageError);
          // Continue with deletion of metadata even if file deletion fails
        }
      }

      // 3. Delete the tutorial metadata
      // RLS will ensure user can only delete their own tutorials
      const { error: deleteError } = await supabase
        .from("tutorial_metadata")
        .delete()
        .eq("id", tutorialId);

      if (deleteError) {
        throw new Error(`Failed to delete tutorial: ${deleteError.message}`);
      }

      // Show success notification
      toast({
        title: "Tutorial deleted",
        description: "The tutorial has been successfully deleted.",
        variant: "default",
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      
      // Show error notification
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      console.error("Error deleting tutorial:", err);
    } finally {
      setIsDeleting(false);
    }
  };

  return { deleteTutorial, isDeleting, error };
};
