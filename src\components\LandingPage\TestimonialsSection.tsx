
import {
  Card,
  CardContent,
} from "@/components/ui/card";

export function TestimonialsSection() {
  const testimonials = [
    {
      rating: 5,
      text: "CodeTutor saved me weeks of documentation work. It generated comprehensive tutorials for our entire codebase in just a few hours. Game changer!",
      name: "<PERSON>",
      title: "Senior Developer at TechCorp",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face"
    },
    {
      rating: 5,
      text: "Perfect for onboarding new team members. Instead of spending days explaining our architecture, I just share the CodeTutor tutorial. Everyone gets up to speed quickly.",
      name: "<PERSON>",
      title: "Team Lead at StartupXYZ",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face"
    },
    {
      rating: 5,
      text: "As an open-source maintainer, CodeTutor helps me create documentation that actually helps developers understand and contribute to my projects. Amazing tool!",
      name: "<PERSON>",
      title: "Open Source Maintainer",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face"
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-gray-800">
            What Developers Say
          </h2>
          <p className="text-xl text-gray-600">
            Join thousands of developers who love CodeTutor
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg">
              <CardContent className="p-8">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <i key={i} className="fa-solid fa-star text-yellow-400"></i>
                  ))}
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.text}"
                </p>
                <div className="flex items-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div>
                    <div className="font-semibold text-gray-800">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.title}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
