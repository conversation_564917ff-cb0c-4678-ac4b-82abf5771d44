import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="bg-gradient-to-r from-tutorial-primary to-tutorial-secondary py-24 text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="container mx-auto px-4 text-center relative">
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight ">
          Transform Code into
          <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
            Knowledge
          </span>
        </h1>
        <p className="text-xl md:text-2xl mb-10 max-w-4xl mx-auto opacity-95 leading-relaxed">
          Generate comprehensive, beginner-friendly tutorials from any GitHub
          repository with the power of AI. Turn complex codebases into
          step-by-step learning experiences.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
          <Link to="/dashboard">
            <Button
              size="lg"
              className="bg-white text-tutorial-primary hover:bg-gray-100 text-lg px-8 py-4"
            >
              Start Free Trial
              <i className="fa-solid fa-arrow-right ml-2"></i>
            </Button>
          </Link>
          <Link to="/public-gallery">
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10 text-lg px-8 py-4"
            >
              Explore Examples
              <i className="fa-solid fa-play ml-2"></i>
            </Button>
          </Link>
        </div>
        <div className="flex justify-center items-center space-x-8 text-sm opacity-80">
          <div className="flex items-center">
            <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
            7-day free trial
          </div>
          <div className="flex items-center">
            <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
            No credit card required
          </div>
          <div className="flex items-center">
            <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
            Cancel anytime
          </div>
        </div>
      </div>
    </section>
  );
}
