
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function PricingSection() {
  const plans = [
    {
      name: "Starter",
      description: "Perfect for individual developers",
      price: "Free",
      features: [
        "3 tutorials per month",
        "Public repositories only",
        "Basic templates",
        "Community support"
      ],
      buttonText: "Get Started Free",
      buttonVariant: "outline" as const,
      popular: false
    },
    {
      name: "Professional",
      description: "For serious developers and small teams",
      price: "$29",
      priceUnit: "/month",
      features: [
        "20 tutorials per month",
        "Private repositories",
        "Advanced file filtering",
        "Multiple output formats",
        "Email support",
        "Custom templates"
      ],
      buttonText: "Start Free Trial",
      buttonVariant: "default" as const,
      popular: true
    },
    {
      name: "Enterprise",
      description: "For large teams and organizations",
      price: "$99",
      priceUnit: "/month",
      features: [
        "Unlimited tutorials",
        "Team collaboration",
        "Custom branding",
        "Priority support",
        "API access",
        "SSO integration"
      ],
      buttonText: "Contact Sales",
      buttonVariant: "outline" as const,
      popular: false
    }
  ];

  return (
    <section id="pricing" className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-gray-800">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the plan that fits your needs. All plans include a 7-day
            free trial.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`border-2 transition-colors duration-300 flex flex-col ${
                plan.popular 
                  ? "border-tutorial-primary shadow-xl relative" 
                  : "border-gray-200 hover:border-tutorial-primary"
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-tutorial-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                <CardDescription className="text-base">
                  {plan.description}
                </CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-800">{plan.price}</span>
                  {plan.priceUnit && (
                    <span className="text-gray-600">{plan.priceUnit}</span>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4 flex-1 flex flex-col">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-auto pt-4">
                  <Link to="/dashboard" className="block">
                    <Button 
                      className={`w-full ${
                        plan.popular 
                          ? "bg-tutorial-primary hover:bg-tutorial-primary/90" 
                          : ""
                      }`}
                      variant={plan.buttonVariant}
                    >
                      {plan.buttonText}
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
