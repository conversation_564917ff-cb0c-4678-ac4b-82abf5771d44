
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

export function CTASection() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-4xl font-bold mb-6 text-gray-800">
          Ready to Transform Your Code?
        </h2>
        <p className="text-xl mb-10 max-w-3xl mx-auto text-gray-600">
          Join thousands of developers who are using CodeTutor to create
          better documentation, onboard team members faster, and share
          knowledge more effectively.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link to="/dashboard">
            <Button
              size="lg"
              className="bg-tutorial-primary text-white hover:bg-tutorial-primary/90 text-lg px-8 py-4"
            >
              Start Your Free Trial
              <i className="fa-solid fa-rocket ml-2"></i>
            </Button>
          </Link>
          <Link to="/gallery">
            <Button size="lg" variant="outline" className="text-lg px-8 py-4">
              View Examples
            </Button>
          </Link>
        </div>
        <p className="text-sm text-gray-500 mt-6">
          No credit card required • 7-day free trial • Cancel anytime
        </p>
      </div>
    </section>
  );
}
