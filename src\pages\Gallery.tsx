
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import NavBar from "@/components/NavBar";
import { BookOpen, Clock, Eye, Plus, Search, Trash2 } from "lucide-react";
import DeleteTutorialDialog from "@/components/DeleteTutorialDialog";
import { useAuth } from "@/hooks/useAuth";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFeaturedTutorials, useRecentTutorials, usePopularTutorials, Tutorial } from "@/hooks/useTutorials";
import { FeaturedTutorialSkeleton, RecentTutorialSkeleton, PopularTutorialSkeleton } from "@/components/TutorialSkeleton";

// Helper functions for time formatting
const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) return "just now";
  if (diffInHours === 1) return "1 hour ago";
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays === 1) return "yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;

  return date.toLocaleDateString();
};

// Language to icon mapping
const languageIcons: Record<string, string> = {
  React: "fa-brands fa-react",
  Python: "fa-brands fa-python",
  "Node.js": "fa-brands fa-node-js",
  Go: "fa-brands fa-golang",
  PHP: "fa-brands fa-php",
  Java: "fa-brands fa-java",
  Angular: "fa-brands fa-angular",
  Redux: "fa-brands fa-react",
  Docker: "fa-brands fa-docker",
  Rust: "fa-brands fa-rust",
  GraphQL: "fa-solid fa-database",
  TypeScript: "fa-brands fa-js",
  JavaScript: "fa-brands fa-js"
};

// Difficulty to color mapping
const difficultyColors: Record<string, string> = {
  Beginner: "bg-green-100 text-green-700",
  Intermediate: "bg-primary-100 text-primary-700",
  Advanced: "bg-red-100 text-red-700"
};

const Gallery = () => {
  const { isSignedIn } = useAuth();
  const { tutorials: featuredTutorials, loading: featuredLoading, error: featuredError } = useFeaturedTutorials();
  const { tutorials: recentTutorials, loading: recentLoading, error: recentError } = useRecentTutorials();
  const { tutorials: popularTutorials, loading: popularLoading, error: popularError } = usePopularTutorials();

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tutorialToDelete, setTutorialToDelete] = useState<{ id: string; title: string } | null>(null);

  // Function to handle tutorial deletion - only for authenticated users
  const handleDeleteClick = (e: React.MouseEvent, tutorial: Tutorial) => {
    if (!isSignedIn) return; // Prevent deletion if not signed in
    
    e.preventDefault(); // Prevent navigation to tutorial details
    e.stopPropagation(); // Prevent event bubbling
    setTutorialToDelete({ id: tutorial.id, title: tutorial.title });
    setDeleteDialogOpen(true);
  };

  // Function to handle successful deletion
  const handleTutorialDeleted = () => {
    // Force a refresh of the tutorial lists
    window.location.reload();
    // Note: In a production app, you would update the state instead of reloading the page
  };

  // Sample user avatar data - since we don't have this in the database yet
  const sampleUsers = ["avatar-1.jpg", "avatar-2.jpg", "avatar-3.jpg", "avatar-4.jpg", "avatar-5.jpg", "avatar-6.jpg", "avatar-7.jpg", "avatar-8.jpg", "avatar-9.jpg"];

  const assignRandomUsers = (tutorial: Tutorial) => {
    const count = Math.floor(Math.random() * 3) + 1; // 1 to 3 random users
    const users: string[] = [];

    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * sampleUsers.length);
      users.push(sampleUsers[randomIndex]);
    }

    return users;
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NavBar />

      {/* Delete Tutorial Dialog - only show if user is signed in */}
      {isSignedIn && tutorialToDelete && (
        <DeleteTutorialDialog
          isOpen={deleteDialogOpen}
          onClose={() => {
            setDeleteDialogOpen(false);
            setTutorialToDelete(null);
          }}
          tutorialId={tutorialToDelete.id}
          tutorialTitle={tutorialToDelete.title}
          onDeleted={handleTutorialDeleted}
        />
      )}

      <main className="container mx-auto px-4 py-8 md:py-12">
        {/* Page Title */}
        <div id="page-title" className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">Tutorial Gallery</h1>
          <p className="text-gray-600 max-w-3xl">Browse through our collection of AI-generated tutorials from GitHub repositories. Find comprehensive guides for popular codebases and frameworks.</p>
        </div>

        {/* Search and Filter Section */}
        <div id="search-filter-section" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Bar */}
            <div className="w-full md:w-1/2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <Input
                  type="text"
                  className="pl-10 pr-4"
                  placeholder="Search tutorials by name, language, or framework..."
                />
              </div>
            </div>

            {/* Filters */}
            <div className="w-full md:w-1/2 flex flex-wrap gap-3">
              <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Languages</option>
                <option value="javascript">JavaScript</option>
                <option value="typescript">TypeScript</option>
                <option value="python">Python</option>
                <option value="java">Java</option>
                <option value="go">Go</option>
                <option value="rust">Rust</option>
              </select>

              <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Difficulties</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>

              <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="popular">Most Popular</option>
              </select>
            </div>
          </div>
        </div>

        {/* Featured Tutorials */}
        {RenderFeatured()}

        {/* Recently Generated */}
        {RenderRecent()}

        {/* Popular Tutorials */}
        {RenderPopulars()}

      </main>

      {/* Footer */}
      <footer id="footer" className="bg-white border-t border-gray-200 mt-12">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center">
                <i className="fa-solid fa-book-open text-primary-600 text-xl mr-2"></i>
                <span className="text-lg font-bold text-gray-800">CodeTutor</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">Automated tutorials from GitHub repositories</p>
            </div>

            <div className="flex space-x-6">
              <a href="#" className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-brands fa-github text-lg"></i>
              </a>
              <a href="#" className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-brands fa-twitter text-lg"></i>
              </a>
              <a href="#" className="text-gray-500 hover:text-primary-600 cursor-pointer">
                <i className="fa-solid fa-envelope text-lg"></i>
              </a>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-6 pt-6 flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-600">
              © 2025 CodeTutor. All rights reserved.
            </div>
            <div className="mt-4 md:mt-0 flex space-x-6">
              <a href="#" className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">Privacy Policy</a>
              <a href="#" className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">Terms of Service</a>
              <a href="#" className="text-sm text-gray-600 hover:text-primary-600 cursor-pointer">Contact</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );

  function RenderPopulars() {
    return <div id="popular-tutorials">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Popular Tutorials</h2>
        <Button variant="link" className="text-primary-600 hover:text-primary-700">
          View All
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {popularLoading ? (
          <>
            <PopularTutorialSkeleton />
            <PopularTutorialSkeleton />
            <PopularTutorialSkeleton />
            <PopularTutorialSkeleton />
            <PopularTutorialSkeleton />
            <PopularTutorialSkeleton />
          </>
        ) : popularError ? (
          <div className="col-span-3 text-center text-red-500 py-12">
            Error loading popular tutorials: {popularError}
          </div>
        ) : popularTutorials.length === 0 ? RenderGenerateFirstTutorial() : RenderPopular()}
      </div>
    </div>;
  }

  function RenderRecent() {
    return <div id="recently-generated" className="mb-12">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Recently Generated</h2>
        <Button variant="link" className="text-primary-600 hover:text-primary-700" asChild>
          <Link to="/">
            <span className="mr-1">Generate New</span>
            <Plus className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {recentLoading ? (
          <>
            <RecentTutorialSkeleton />
            <RecentTutorialSkeleton />
            <RecentTutorialSkeleton />
            <RecentTutorialSkeleton />
          </>
        ) : recentError ? (
          <div className="col-span-4 text-center text-red-500 py-12">
            Error loading recent tutorials: {recentError}
          </div>
        ) : recentTutorials.length === 0 ? (
          RenderGenerateFirstTutorial()
        ) : RenderGenerateRecent()}
      </div>
    </div>;
  }

  function RenderGenerateRecent(): React.ReactNode {
    return recentTutorials.map((tutorial) => (
      <div key={tutorial.id} className="relative group">
        <Link to={`/tutorial/${tutorial.id}`} className="block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
          <div className={`h-[140px] bg-gradient-to-r ${tutorial.backgroundColor} relative`}>
            <div className="absolute inset-0 flex items-center justify-center">
              <img className="w-20 h-20 object-contain" src={tutorial.imageSrc} alt={tutorial.title} />
            </div>
            <div className="absolute bottom-3 left-3 flex items-center">
              <i className={`${languageIcons[tutorial.language || ''] || 'fa-solid fa-code'} text-white text-lg mr-2`}></i>
              <span className="text-white font-medium text-sm">{tutorial.language}</span>
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-semibold text-gray-800 mb-2">{tutorial.title}</h3>
            <p className="text-gray-600 text-xs mb-3">{tutorial.description}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center text-gray-500">
                <BookOpen className="h-3 w-3 mr-1" />
                <span>{tutorial.chaptersCount} chapters</span>
              </div>
              <div className="flex items-center text-gray-500">
                <Clock className="h-3 w-3 mr-1" />
                <span>{formatTimeAgo(tutorial.createdAt)}</span>
              </div>
            </div>
          </div>
        </Link>

        {/* Delete Button - Only visible on hover and when user is signed in */}
        {isSignedIn && (
          <button
            onClick={(e) => handleDeleteClick(e, tutorial)}
            className="absolute top-2 right-2 p-2 rounded-full bg-white shadow-md text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity z-10"
            title="Delete tutorial"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}
      </div>
    ));
  }

  function RenderGenerateFirstTutorial(): React.ReactNode {
    return <div className="col-span-3 text-center py-12">
      <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
      <h3 className="mt-4 text-lg font-medium">No popular tutorials yet</h3>
      <p className="mt-2 text-muted-foreground">
        Generate your first tutorial to see it here
      </p>
      <Link
        to="/"
        className="mt-4 inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
      >
        Generate Tutorial
      </Link>
    </div>;
  }

  function RenderPopular(): React.ReactNode {
    return popularTutorials.map((tutorial) => {
      const tutorialUsers = assignRandomUsers(tutorial);
      return (
        <Link key={tutorial.id} to={`/tutorial/${tutorial.id}`} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow flex">
          <div className={`w-1/3 bg-gradient-to-r ${tutorial.backgroundColor} relative`}>
            <div className="absolute inset-0 flex items-center justify-center">
              <img className="w-16 h-16 object-contain" src={tutorial.imageSrc} alt={tutorial.title} />
            </div>
          </div>
          <div className="w-2/3 p-4">
            <div className="flex items-center mb-1">
              <h3 className="font-semibold text-gray-800">{tutorial.title}</h3>
              <span className="ml-2 bg-indigo-100 text-indigo-700 text-xs px-2 py-0.5 rounded-full">Popular</span>
            </div>
            <p className="text-gray-600 text-xs mb-2">{tutorial.description}</p>
            <div className="flex items-center text-xs text-gray-500 mb-2">
              <Eye className="h-3 w-3 mr-1" />
              <span>{tutorial.views?.toLocaleString()} views</span>
              <span className="mx-2">•</span>
              <BookOpen className="h-3 w-3 mr-1" />
              <span>{tutorial.chaptersCount} chapters</span>
            </div>
            <div className="flex -space-x-1">
              {tutorialUsers.slice(0, 3).map((user, idx) => (
                <img
                  key={idx}
                  src={`https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/${user}`}
                  className="w-5 h-5 rounded-full border border-white"
                  alt="User" />
              ))}
            </div>
          </div>
        </Link>
      );
    });
  }

  function RenderFeatured() {
    return <div id="featured-tutorials" className="mb-12">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Featured Tutorials</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {featuredLoading ? (
          <>
            <FeaturedTutorialSkeleton />
            <FeaturedTutorialSkeleton />
            <FeaturedTutorialSkeleton />
          </>
        ) : featuredError ? (
          <div className="col-span-3 text-center text-red-500 py-12">
            Error loading featured tutorials: {featuredError}
          </div>
        ) : featuredTutorials.length === 0 ? (
          <div className="col-span-3 text-center py-12">
            <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No featured tutorials yet</h3>
            <p className="mt-2 text-muted-foreground">
              Generate your first featured tutorial to see it here
            </p>
            <Link
              to="/"
              className="mt-4 inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
            >
              Generate Tutorial
            </Link>
          </div>
        ) : (
          featuredTutorials.map((tutorial) => {
            const tutorialUsers = assignRandomUsers(tutorial);
            return (
              <div key={tutorial.id} className="relative group">
                <Link to={`/tutorial/${tutorial.id}`} className="block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  <div className={`h-[180px] bg-gradient-to-r ${tutorial.backgroundColor} relative`}>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <img className="w-24 h-24 object-contain" src={tutorial.imageSrc} alt={tutorial.title} />
                    </div>
                    <div className="absolute top-3 right-3 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      Featured
                    </div>
                    <div className="absolute bottom-3 left-3 flex items-center">
                      <i className={`${languageIcons[tutorial.language || ''] || 'fa-solid fa-code'} text-white text-xl mr-2`}></i>
                      <span className="text-white font-medium">{tutorial.language}</span>
                    </div>
                  </div>
                  <div className="p-5">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-lg text-gray-800">{tutorial.title}</h3>
                      {tutorial.difficulty && (
                        <span className={`${difficultyColors[tutorial.difficulty]} text-xs px-2 py-1 rounded-full`}>
                          {tutorial.difficulty}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mb-4">{tutorial.description}</p>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-gray-500">
                        <BookOpen className="h-4 w-4 mr-1" />
                        <span>{tutorial.chaptersCount} chapters</span>
                      </div>
                      <div className="flex items-center text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{formatTimeAgo(tutorial.createdAt)}</span>
                      </div>
                    </div>
                    <div className="mt-4 flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {tutorialUsers.slice(0, 3).map((user, idx) => (
                          <img
                            key={idx}
                            src={`https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/${user}`}
                            className="w-6 h-6 rounded-full border border-white"
                            alt="User" />
                        ))}
                        {tutorialUsers.length > 3 && (
                          <span className="flex items-center justify-center w-6 h-6 rounded-full border border-white bg-gray-200 text-xs">
                            +{tutorialUsers.length - 3}
                          </span>
                        )}
                      </div>
                      <Button variant="link" className="text-primary-600 hover:text-primary-700">
                        View Tutorial
                      </Button>
                    </div>
                  </div>
                </Link>

                {/* Delete Button - Only visible on hover and when user is signed in */}
                {isSignedIn && (
                  <button
                    onClick={(e) => handleDeleteClick(e, tutorial)}
                    className="absolute top-2 right-2 p-2 rounded-full bg-white shadow-md text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                    title="Delete tutorial"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>;
  }
};

export default Gallery;
