
import { ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";
import AuthComponent from "./AuthComponent";

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isSignedIn, isLoaded } = useAuth();

  // Show loading state while auth is being determined
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Show auth interface if not signed in
  if (!isSignedIn) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-4 text-center">Sign in required</h1>
          <p className="text-gray-600 mb-6 text-center">You need to sign in to access this page.</p>
          <AuthComponent />
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
