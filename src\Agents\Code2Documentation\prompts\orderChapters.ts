export const ORDER_CHAPTERS_PROMPT = `
Given the following project abstractions and their relationships for the project \`\`\`\` \${project_name} \`\`\`\`:

Abstractions (Index # Name)\${list_lang_note}:
\${abstraction_listing}

Context about relationships and project summary:
\${context}

If you are going to make a tutorial for \`\`\`\` \${project_name} \`\`\`\`, what is the best order to explain these abstractions, from first to last?
Ideally, first explain those that are the most important or foundational, perhaps user-facing concepts or entry points. Then move to more detailed, lower-level implementation details or supporting concepts.

Output the ordered list of abstraction indices, including the name in a comment for clarity. Use the format \`idx # AbstractionName\`.

\`\`\`yaml
- 2 # FoundationalConcept
- 0 # CoreClassA
- 1 # CoreClassB (uses CoreClassA)
- ...
\`\`\`

Now, provide the YAML output:
`;
