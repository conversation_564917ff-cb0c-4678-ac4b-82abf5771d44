
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useMemo } from 'react';
import type { Database } from '@/integrations/supabase/types';

const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4MTQ2MzEsImV4cCI6MjA2MjM5MDYzMX0.K6tH_zUWz3gFOB9WStMmfDQY8y_jjyi9d-HB4tmSzho";

export const useSupabase = (): SupabaseClient<Database> => {
  const supabaseClient = useMemo(() => {
    return createClient<Database>(
      SUPABASE_URL,
      SUPABASE_ANON_KEY,
      {
        auth: {
          storage: localStorage,
          persistSession: true,
          autoRefreshToken: true,
        }
      }
    );
  }, []);

  return supabaseClient;
};
