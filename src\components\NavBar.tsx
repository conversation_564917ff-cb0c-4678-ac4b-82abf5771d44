
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import AuthComponent from "./AuthComponent";

const NavBar = () => {
  const { isSignedIn } = useAuth();
  const location = useLocation();
  
  // Show admin link only when signed in
  const showAdminLink = isSignedIn;
  
  // Define public routes where auth component should not be shown unless user is already signed in
  const publicRoutes = ['/gallery', '/public-gallery', '/tutorial'];
  const isPublicRoute = publicRoutes.some(route => location.pathname.startsWith(route));
  
  // Show auth component if user is signed in OR if we're not on a public route
  const showAuthComponent = isSignedIn || !isPublicRoute;

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/" className="flex items-center cursor-pointer">
            <i className="fa-solid fa-book-open text-primary-600 text-2xl mr-2"></i>
            <span className="text-xl font-bold text-gray-800">CodeTutor</span>
          </Link>
          <div className="hidden md:flex ml-10 space-x-6">
            <Link to="/dashboard" className="text-gray-500 hover:text-primary-600 cursor-pointer">Dashboard</Link>
            <Link to="/gallery" className="text-gray-800 hover:text-primary-600 font-medium cursor-pointer">Gallery</Link>
            <Link to="/public-gallery" className="text-gray-500 hover:text-primary-600 cursor-pointer">Public Gallery</Link>
            {showAdminLink && (
              <Link to="/admin" className="text-gray-500 hover:text-primary-600 cursor-pointer">Admin</Link>
            )}
            <Link to="#" className="text-gray-500 hover:text-primary-600 cursor-pointer">Docs</Link>
            <Link to="#" className="text-gray-500 hover:text-primary-600 cursor-pointer">Pricing</Link>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <button className="text-gray-500 hover:text-gray-700 cursor-pointer">
            <i className="fa-regular fa-circle-question"></i>
          </button>
          <button className="text-gray-500 hover:text-gray-700 cursor-pointer">
            <i className="fa-regular fa-bell"></i>
          </button>
          {showAuthComponent && <AuthComponent />}
        </div>
      </div>
    </header>
  );
};

export default NavBar;
